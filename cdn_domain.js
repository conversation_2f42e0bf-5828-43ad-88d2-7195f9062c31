var img_data_domain = window.global_cdn
var op = window.global_op_server

function load_js_css(url, type) {
    var oHead = document.getElementsByTagName("HEAD").item(0);
    if (type == 'js') {
        var oLoad_url = document.createElement("script");
        oLoad_url.type = "text/javascript";
        oLoad_url.src = url;
    } else {
        var oLoad_url = document.createElement("link");
        oLoad_url.type = "text/css";
        oLoad_url.rel = "stylesheet";
        oLoad_url.href = url;
    }
    oHead.appendChild(oLoad_url);
}

function generateRandomString(length) {
    var result = '';
    var characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    for (var i = 0; i < length; i++) {
        var randomIndex = Math.floor(Math.random() * characters.length);
        result += characters.charAt(randomIndex);
    }
    return result;
}

var op_service = "https://" + generateRandomString(15) +"."+ op;//OP服务地址
